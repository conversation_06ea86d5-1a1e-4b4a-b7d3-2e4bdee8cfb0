#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看增强版Excel处理报告
"""

import pandas as pd
import os

def view_enhanced_excel_report(file_path='sku_summary_report_enhanced.xlsx'):
    """查看增强版Excel报告内容"""
    if not os.path.exists(file_path):
        print(f"报告文件 {file_path} 不存在")
        return
    
    try:
        # 读取所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"报告文件: {file_path}")
        print(f"工作表: {excel_file.sheet_names}")
        print("=" * 80)
        
        # 显示SKU汇总（按指定顺序）
        if 'SKU汇总' in excel_file.sheet_names:
            sku_df = pd.read_excel(file_path, sheet_name='SKU汇总')
            print("\nSKU汇总表（按指定顺序排序）:")
            print("-" * 80)
            
            # 设置显示格式
            pd.set_option('display.max_rows', None)
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            pd.set_option('display.max_colwidth', 20)
            
            print(sku_df.to_string(index=False))
            
            # 统计信息
            total_planned = sku_df['计划数量'].sum()
            total_received = sku_df['验收数量'].sum()
            total_difference = sku_df['差异数量'].sum()
            
            print(f"\n汇总统计:")
            print(f"  总计划数量: {total_planned:,}")
            print(f"  总验收数量: {total_received:,}")
            print(f"  总差异数量: {total_difference:,}")
            if total_planned > 0:
                print(f"  总差异率: {total_difference / total_planned * 100:.2f}%")
        
        # 显示汇总统计
        if '汇总统计' in excel_file.sheet_names:
            summary_df = pd.read_excel(file_path, sheet_name='汇总统计')
            print("\n\n汇总统计:")
            print("-" * 40)
            for _, row in summary_df.iterrows():
                print(f"  {row['项目']}: {row['数值']:,}")
        
        # 显示处理状态
        if '处理状态' in excel_file.sheet_names:
            status_df = pd.read_excel(file_path, sheet_name='处理状态')
            print("\n\n文件处理状态:")
            print("-" * 60)
            for _, row in status_df.iterrows():
                print(f"文件: {row['file_name']}")
                print(f"  状态: {row['status']}")
                print(f"  SKU数量: {row['sku_count']}")
                print(f"  计划数量: {row.get('total_planned', 'N/A')}")
                print(f"  验收数量: {row.get('total_received', 'N/A')}")
                print(f"  差异数量: {row.get('total_difference', 'N/A')}")
                print()
        
        # 显示SKU排序说明
        if 'SKU排序说明' in excel_file.sheet_names:
            order_df = pd.read_excel(file_path, sheet_name='SKU排序说明')
            print("\nSKU排序说明:")
            print("-" * 50)
            
            # 分别显示指定顺序和字母顺序的SKU
            specified_skus = order_df[order_df['说明'] == '指定顺序']
            unspecified_skus = order_df[order_df['说明'] != '指定顺序']
            
            if not specified_skus.empty:
                print("指定顺序的SKU:")
                for _, row in specified_skus.iterrows():
                    print(f"  {row['排序']:2d}. {row['SKU']}")
            
            if not unspecified_skus.empty:
                print(f"\n字母顺序的SKU（未在指定列表中）:")
                for _, row in unspecified_skus.iterrows():
                    print(f"  {row['排序']:2d}. {row['SKU']}")
        
        # 显示错误文件（如果有）
        if '错误文件' in excel_file.sheet_names:
            error_df = pd.read_excel(file_path, sheet_name='错误文件')
            if not error_df.empty:
                print("\n错误文件:")
                print("-" * 40)
                print(error_df.to_string(index=False))
        
        # 分析差异情况
        if 'SKU汇总' in excel_file.sheet_names:
            sku_df = pd.read_excel(file_path, sheet_name='SKU汇总')
            
            # 找出有差异的SKU
            with_difference = sku_df[sku_df['差异数量'] != 0]
            if not with_difference.empty:
                print(f"\n\n差异分析:")
                print("-" * 60)
                print(f"有差异的SKU数量: {len(with_difference)}")
                
                # 按差异绝对值排序
                with_difference_sorted = with_difference.reindex(
                    with_difference['差异数量'].abs().sort_values(ascending=False).index
                )
                
                print("差异最大的前10个SKU:")
                for _, row in with_difference_sorted.head(10).iterrows():
                    print(f"  {row['SKU']:<12}: 差异{row['差异数量']:>6} ({row['差异率(%)']:>6.1f}%)")
        
    except Exception as e:
        print(f"读取报告文件时出错: {str(e)}")

def compare_reports():
    """比较原版和增强版报告"""
    original_file = 'sku_summary_report.xlsx'
    enhanced_file = 'sku_summary_report_enhanced.xlsx'
    
    if not os.path.exists(original_file) or not os.path.exists(enhanced_file):
        print("需要两个报告文件才能进行比较")
        return
    
    try:
        # 读取两个报告的SKU汇总
        original_df = pd.read_excel(original_file, sheet_name='SKU汇总')
        enhanced_df = pd.read_excel(enhanced_file, sheet_name='SKU汇总')
        
        print("报告比较:")
        print("=" * 60)
        print(f"原版报告SKU数量: {len(original_df)}")
        print(f"增强版报告SKU数量: {len(enhanced_df)}")
        
        # 比较SKU列表
        original_skus = set(original_df['SKU'])
        enhanced_skus = set(enhanced_df['SKU'])
        
        common_skus = original_skus & enhanced_skus
        only_in_original = original_skus - enhanced_skus
        only_in_enhanced = enhanced_skus - original_skus
        
        print(f"共同SKU数量: {len(common_skus)}")
        if only_in_original:
            print(f"仅在原版中的SKU: {sorted(only_in_original)}")
        if only_in_enhanced:
            print(f"仅在增强版中的SKU: {sorted(only_in_enhanced)}")
        
    except Exception as e:
        print(f"比较报告时出错: {str(e)}")

if __name__ == "__main__":
    print("查看增强版Excel处理报告")
    print("=" * 50)
    view_enhanced_excel_report()
    
    print("\n\n" + "=" * 50)
    print("比较原版和增强版报告")
    print("=" * 50)
    compare_reports()
