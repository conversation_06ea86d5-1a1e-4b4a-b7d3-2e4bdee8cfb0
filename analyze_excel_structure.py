#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件结构分析脚本
用于分析Excel文件的工作表结构、列名和数据格式
"""

import pandas as pd
import os
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_excel_file(file_path):
    """
    分析单个Excel文件的结构
    
    Args:
        file_path (str): Excel文件路径
    
    Returns:
        dict: 文件分析结果
    """
    try:
        logger.info(f"正在分析文件: {file_path}")
        
        # 获取所有工作表名称
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        analysis_result = {
            'file_name': os.path.basename(file_path),
            'sheet_count': len(sheet_names),
            'sheet_names': sheet_names,
            'sheets_analysis': {}
        }
        
        # 分析每个工作表
        for sheet_name in sheet_names:
            try:
                # 读取工作表数据
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                sheet_analysis = {
                    'row_count': len(df),
                    'column_count': len(df.columns),
                    'columns': list(df.columns),
                    'data_types': df.dtypes.to_dict(),
                    'sample_data': df.head(3).to_dict('records') if len(df) > 0 else [],
                    'potential_sku_columns': [],
                    'potential_quantity_columns': []
                }
                
                # 查找可能的SKU列（包含SKU、商品、产品等关键词）
                sku_keywords = ['sku', 'SKU', '商品', '产品', '货号', '编码', '代码', 'code', 'item']
                for col in df.columns:
                    col_str = str(col).lower()
                    if any(keyword.lower() in col_str for keyword in sku_keywords):
                        sheet_analysis['potential_sku_columns'].append(col)
                
                # 查找可能的数量列（包含数量、qty等关键词）
                qty_keywords = ['数量', 'qty', 'quantity', '件数', '个数', 'count', 'amount', '总数']
                for col in df.columns:
                    col_str = str(col).lower()
                    if any(keyword.lower() in col_str for keyword in qty_keywords):
                        sheet_analysis['potential_quantity_columns'].append(col)
                
                analysis_result['sheets_analysis'][sheet_name] = sheet_analysis
                
            except Exception as e:
                logger.error(f"分析工作表 {sheet_name} 时出错: {str(e)}")
                analysis_result['sheets_analysis'][sheet_name] = {'error': str(e)}
        
        return analysis_result
        
    except Exception as e:
        logger.error(f"分析文件 {file_path} 时出错: {str(e)}")
        return {'file_name': os.path.basename(file_path), 'error': str(e)}

def main():
    """主函数"""
    # 示例文件路径
    sample_files = [
        'wb 7月送仓/31851009-shk-excel.xlsx',
        'wb 7月送仓/31468043-income-goods-excel.xlsx'
    ]
    
    print("=" * 80)
    print("Excel文件结构分析报告")
    print("=" * 80)
    
    for file_path in sample_files:
        if os.path.exists(file_path):
            result = analyze_excel_file(file_path)
            
            print(f"\n文件: {result['file_name']}")
            print("-" * 60)
            
            if 'error' in result:
                print(f"错误: {result['error']}")
                continue
            
            print(f"工作表数量: {result['sheet_count']}")
            print(f"工作表名称: {result['sheet_names']}")
            
            for sheet_name, sheet_info in result['sheets_analysis'].items():
                print(f"\n  工作表: {sheet_name}")
                if 'error' in sheet_info:
                    print(f"    错误: {sheet_info['error']}")
                    continue
                
                print(f"    行数: {sheet_info['row_count']}")
                print(f"    列数: {sheet_info['column_count']}")
                print(f"    列名: {sheet_info['columns']}")
                print(f"    可能的SKU列: {sheet_info['potential_sku_columns']}")
                print(f"    可能的数量列: {sheet_info['potential_quantity_columns']}")
                
                if sheet_info['sample_data']:
                    print("    样本数据:")
                    for i, row in enumerate(sheet_info['sample_data'][:2]):
                        print(f"      行{i+1}: {row}")
        else:
            print(f"\n文件不存在: {file_path}")

if __name__ == "__main__":
    main()
