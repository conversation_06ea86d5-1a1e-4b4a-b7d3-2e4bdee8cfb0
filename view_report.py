#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看Excel处理报告
"""

import pandas as pd
import os

def view_excel_report(file_path='sku_summary_report.xlsx'):
    """查看Excel报告内容"""
    if not os.path.exists(file_path):
        print(f"报告文件 {file_path} 不存在")
        return
    
    try:
        # 读取所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"报告文件: {file_path}")
        print(f"工作表: {excel_file.sheet_names}")
        print("=" * 80)
        
        # 显示SKU汇总
        if 'SKU汇总' in excel_file.sheet_names:
            sku_df = pd.read_excel(file_path, sheet_name='SKU汇总')
            print("\nSKU汇总表:")
            print("-" * 40)
            print(sku_df.to_string(index=False))
            print(f"\n总计: {len(sku_df)} 个唯一SKU, 总数量: {sku_df['总数量'].sum()}")
        
        # 显示处理状态
        if '处理状态' in excel_file.sheet_names:
            status_df = pd.read_excel(file_path, sheet_name='处理状态')
            print("\n\n文件处理状态:")
            print("-" * 40)
            for _, row in status_df.iterrows():
                print(f"文件: {row['file_name']}")
                print(f"  状态: {row['status']}")
                print(f"  SKU数量: {row['sku_count']}")
                print(f"  总数量: {row['total_quantity']}")
                print()
        
        # 显示错误文件（如果有）
        if '错误文件' in excel_file.sheet_names:
            error_df = pd.read_excel(file_path, sheet_name='错误文件')
            if not error_df.empty:
                print("\n错误文件:")
                print("-" * 40)
                print(error_df.to_string(index=False))
        
    except Exception as e:
        print(f"读取报告文件时出错: {str(e)}")

if __name__ == "__main__":
    view_excel_report()
