#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看Ozon分析报告的脚本
"""

import pandas as pd
import os

def view_report(file_path):
    """查看Excel报告的内容"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    print(f"📊 查看报告: {os.path.basename(file_path)}")
    print("="*80)
    
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表: {excel_file.sheet_names}")
        
        # 查看主报告
        if 'SKU差异汇总报告' in excel_file.sheet_names:
            df = pd.read_excel(file_path, sheet_name='SKU差异汇总报告')
            print(f"\n📋 SKU差异汇总报告 (共{len(df)}行):")
            print("-" * 60)
            print(df.to_string(index=False))
            
            # 统计信息
            print(f"\n📊 统计信息:")
            print(f"总SKU数量: {len(df)}")
            print(f"正常SKU: {len(df[df['差异数量'] == 0])}")
            print(f"缺货SKU: {len(df[df['差异数量'] > 0])}")
            print(f"超收SKU: {len(df[df['差异数量'] < 0])}")
            print(f"总发货数量: {df['发货数量'].sum():,}")
            print(f"总验收数量: {df['验收数量'].sum():,}")
            print(f"总差异数量: {df['差异数量'].sum():,}")
        
        # 查看存在差异的SKU
        if '存在差异的SKU' in excel_file.sheet_names:
            df_diff = pd.read_excel(file_path, sheet_name='存在差异的SKU')
            print(f"\n⚠️  存在差异的SKU (共{len(df_diff)}行):")
            print("-" * 60)
            print(df_diff.head(10).to_string(index=False))
            if len(df_diff) > 10:
                print(f"... 还有 {len(df_diff) - 10} 行数据")
        
        # 查看统计汇总
        if '统计汇总' in excel_file.sheet_names:
            df_summary = pd.read_excel(file_path, sheet_name='统计汇总')
            print(f"\n📈 统计汇总:")
            print("-" * 60)
            print(df_summary.to_string(index=False))
        
    except Exception as e:
        print(f"读取报告时出错: {e}")

# 查看最新的报告
report_file = "ozon_batch_sku_report_20250823_231412.xlsx"
view_report(report_file)
