#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件处理程序
用于分析和汇总Excel文件中的SKU和数量信息
"""

import pandas as pd
import os
import logging
from pathlib import Path
from collections import defaultdict
import glob
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ExcelProcessor:
    """Excel文件处理器"""
    
    def __init__(self, directory_path):
        """
        初始化处理器
        
        Args:
            directory_path (str): 包含Excel文件的目录路径
        """
        self.directory_path = directory_path
        self.sku_summary = defaultdict(int)
        self.processed_files = []
        self.error_files = []
        
        # 定义可能的SKU和数量列名模式
        self.sku_patterns = [
            'sku', 'SKU', '商品', '产品', '货号', '编码', '代码', 'code', 'item',
            'Баркод', 'Артикул', 'Артикул поставщика', 'баркод', 'артикул'
        ]
        
        self.quantity_patterns = [
            '数量', 'qty', 'quantity', '件数', '个数', 'count', 'amount', '总数',
            'Количество', 'количество', 'шт'
        ]
    
    def find_excel_files(self):
        """
        查找目录中的所有Excel文件
        
        Returns:
            list: Excel文件路径列表
        """
        excel_files = []
        patterns = ['*.xlsx', '*.xls']
        
        for pattern in patterns:
            files = glob.glob(os.path.join(self.directory_path, pattern))
            excel_files.extend(files)
        
        logger.info(f"找到 {len(excel_files)} 个Excel文件")
        return excel_files
    
    def identify_columns(self, df):
        """
        识别DataFrame中的SKU和数量列
        
        Args:
            df (pandas.DataFrame): 数据框
            
        Returns:
            tuple: (sku_column, quantity_column)
        """
        sku_column = None
        quantity_column = None
        
        # 查找SKU列
        for col in df.columns:
            col_str = str(col).lower()
            if any(pattern.lower() in col_str or col_str in pattern.lower() 
                   for pattern in self.sku_patterns):
                sku_column = col
                break
        
        # 查找数量列
        for col in df.columns:
            col_str = str(col).lower()
            if any(pattern.lower() in col_str or col_str in pattern.lower() 
                   for pattern in self.quantity_patterns):
                # 检查列是否包含数值数据
                if pd.api.types.is_numeric_dtype(df[col]):
                    quantity_column = col
                    break
        
        return sku_column, quantity_column
    
    def process_file(self, file_path):
        """
        处理单个Excel文件
        
        Args:
            file_path (str): Excel文件路径
            
        Returns:
            dict: 处理结果
        """
        try:
            logger.info(f"正在处理文件: {os.path.basename(file_path)}")
            
            # 读取Excel文件的所有工作表
            excel_file = pd.ExcelFile(file_path)
            file_sku_count = defaultdict(int)
            
            for sheet_name in excel_file.sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    if df.empty:
                        logger.warning(f"工作表 {sheet_name} 为空，跳过")
                        continue
                    
                    sku_column, quantity_column = self.identify_columns(df)
                    
                    if sku_column is None:
                        logger.warning(f"在 {sheet_name} 中未找到SKU列，跳过")
                        continue
                    
                    logger.info(f"在工作表 {sheet_name} 中找到SKU列: {sku_column}")
                    
                    if quantity_column:
                        logger.info(f"找到数量列: {quantity_column}")
                        # 按SKU汇总数量
                        sku_qty_sum = df.groupby(sku_column)[quantity_column].sum()
                        for sku, qty in sku_qty_sum.items():
                            if pd.notna(sku) and pd.notna(qty):
                                file_sku_count[str(sku)] += int(qty)
                    else:
                        logger.info("未找到数量列，按SKU出现次数计算")
                        # 计算每个SKU的出现次数
                        sku_counts = df[sku_column].value_counts()
                        for sku, count in sku_counts.items():
                            if pd.notna(sku):
                                file_sku_count[str(sku)] += count
                
                except Exception as e:
                    logger.error(f"处理工作表 {sheet_name} 时出错: {str(e)}")
            
            # 更新总汇总
            for sku, qty in file_sku_count.items():
                self.sku_summary[sku] += qty
            
            result = {
                'file_name': os.path.basename(file_path),
                'status': 'success',
                'sku_count': len(file_sku_count),
                'total_quantity': sum(file_sku_count.values()),
                'skus': dict(file_sku_count)
            }
            
            self.processed_files.append(result)
            logger.info(f"成功处理文件 {os.path.basename(file_path)}: {len(file_sku_count)} 个SKU")
            
            return result
            
        except Exception as e:
            error_msg = f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}"
            logger.error(error_msg)
            
            error_result = {
                'file_name': os.path.basename(file_path),
                'status': 'error',
                'error': str(e)
            }
            
            self.error_files.append(error_result)
            return error_result
    
    def process_all_files(self):
        """处理所有Excel文件"""
        excel_files = self.find_excel_files()
        
        if not excel_files:
            logger.warning("未找到Excel文件")
            return
        
        logger.info(f"开始处理 {len(excel_files)} 个文件")
        
        for file_path in excel_files:
            self.process_file(file_path)
        
        logger.info("所有文件处理完成")
    
    def generate_summary_report(self):
        """生成汇总报告"""
        report = {
            'processing_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_files_processed': len(self.processed_files),
            'total_files_with_errors': len(self.error_files),
            'total_unique_skus': len(self.sku_summary),
            'total_quantity': sum(self.sku_summary.values()),
            'sku_summary': dict(self.sku_summary),
            'processed_files': self.processed_files,
            'error_files': self.error_files
        }
        
        return report
    
    def save_results_to_excel(self, output_file='sku_summary_report.xlsx'):
        """
        将结果保存到Excel文件
        
        Args:
            output_file (str): 输出文件名
        """
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # SKU汇总表
                sku_df = pd.DataFrame([
                    {'SKU': sku, '总数量': qty} 
                    for sku, qty in sorted(self.sku_summary.items())
                ])
                sku_df.to_excel(writer, sheet_name='SKU汇总', index=False)
                
                # 文件处理状态表
                if self.processed_files:
                    files_df = pd.DataFrame(self.processed_files)
                    files_df.to_excel(writer, sheet_name='处理状态', index=False)
                
                # 错误文件表
                if self.error_files:
                    error_df = pd.DataFrame(self.error_files)
                    error_df.to_excel(writer, sheet_name='错误文件', index=False)
            
            logger.info(f"结果已保存到: {output_file}")
            
        except Exception as e:
            logger.error(f"保存Excel文件时出错: {str(e)}")
    
    def print_summary(self):
        """打印汇总信息"""
        print("\n" + "="*80)
        print("Excel文件处理汇总报告")
        print("="*80)
        print(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"成功处理文件数: {len(self.processed_files)}")
        print(f"错误文件数: {len(self.error_files)}")
        print(f"唯一SKU总数: {len(self.sku_summary)}")
        print(f"总数量: {sum(self.sku_summary.values())}")
        
        if self.error_files:
            print(f"\n错误文件:")
            for error_file in self.error_files:
                print(f"  - {error_file['file_name']}: {error_file['error']}")
        
        print(f"\nSKU汇总 (前20个):")
        sorted_skus = sorted(self.sku_summary.items(), key=lambda x: x[1], reverse=True)
        for sku, qty in sorted_skus[:20]:
            print(f"  {sku}: {qty}")
        
        if len(sorted_skus) > 20:
            print(f"  ... 还有 {len(sorted_skus) - 20} 个SKU")


def main():
    """主函数"""
    directory_path = "wb 7月送仓"
    
    if not os.path.exists(directory_path):
        print(f"错误: 目录 {directory_path} 不存在")
        return
    
    # 创建处理器并处理文件
    processor = ExcelProcessor(directory_path)
    processor.process_all_files()
    
    # 生成并显示报告
    processor.print_summary()
    
    # 保存结果到Excel文件
    processor.save_results_to_excel()
    
    print(f"\n详细日志已保存到: excel_processing.log")
    print(f"汇总结果已保存到: sku_summary_report.xlsx")


if __name__ == "__main__":
    main()
