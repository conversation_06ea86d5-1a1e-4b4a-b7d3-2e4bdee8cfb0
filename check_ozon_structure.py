#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Ozon文件结构的脚本
"""

import pandas as pd
import os

def check_file_structure(file_path, file_type):
    """检查文件结构"""
    print(f"\n{'='*60}")
    print(f"检查{file_type}文件: {os.path.basename(file_path)}")
    print(f"{'='*60}")
    
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表: {excel_file.sheet_names}")
        
        if file_type == "验收报告":
            # 检查"Отчёт по поставке"工作表
            if "Отчёт по поставке" in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name="Отчёт по поставке")
                print(f"\n工作表'Отчёт по поставке': {len(df)}行, {len(df.columns)}列")
                print(f"列名: {list(df.columns)}")
                print("\n前5行数据:")
                print(df.head().to_string())
            else:
                print("未找到'Отчёт по поставке'工作表")
        
        elif file_type == "发货数据":
            # 检查"Товарный состав"工作表
            if "Товарный состав" in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name="Товарный состав")
                print(f"\n工作表'Товарный состав': {len(df)}行, {len(df.columns)}列")
                print(f"列名: {list(df.columns)}")
                print("\n前5行数据:")
                print(df.head().to_string())
            else:
                print("未找到'Товарный состав'工作表")
        
    except Exception as e:
        print(f"错误: {e}")

# 检查发货数据文件
check_file_structure("ozon 7月送仓/2000026537595-data.xlsx", "发货数据")

# 检查验收报告文件
check_file_structure("ozon 7月送仓/supply-2000024950553-acceptance-report.xlsx", "验收报告")
