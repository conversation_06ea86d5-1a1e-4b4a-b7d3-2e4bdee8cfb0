# Excel文件SKU分析器 - 最终版本

这是一个功能完整的Excel文件SKU分析和处理程序，支持验收数量、差异计算和自定义SKU排序。

## 🎯 核心功能

### ✅ **已实现的功能**
- **自定义SKU排序**: 严格按照指定的SKU顺序进行排序
- **验收数量支持**: 支持计划数量、验收数量和差异计算
- **多语言识别**: 自动识别中文、英文、俄文的列名
- **批量处理**: 一次性处理目录中的所有Excel文件
- **详细报告**: 生成包含多个工作表的完整Excel报告
- **错误处理**: 完善的错误处理和日志记录
- **灵活配置**: 支持命令行参数和配置文件

### 📊 **指定的SKU排序顺序**
```
MLN101-1, MLN101-2, MLN101-3, MLN101-4, MLN101-5, MLN101-6,
MLN102-1, MLN102-2, MLN102-3, MLN102-4, MLN108-1,
MNT100-1, MNT101-1, MNT101-2, MNT102-1, ODD100-1,
MLN01-1, MLN01-2, MLN01-3, MLN01-4, MLN02-3,
MLN103-3, ODD100-2, MNT105-1
```

## 📁 文件说明

### 🔧 **核心程序文件**
- `excel_analyzer_final.py` - **主程序**，提供完整的命令行界面
- `excel_processor_enhanced.py` - 增强版处理器，支持验收数量和自定义排序
- `view_enhanced_report.py` - 报告查看工具

### 📋 **辅助工具**
- `analyze_excel_structure.py` - 文件结构分析工具
- `excel_processor.py` - 基础版处理器
- `view_report.py` - 基础报告查看工具

### 📄 **配置和数据文件**
- `sample_acceptance_data.json` - 示例验收数据
- `excel_processing.log` - 处理日志
- `sku_summary_report_final.xlsx` - 最终报告

## 🚀 使用方法

### 安装依赖
```bash
pip install pandas openpyxl
```

### 基本使用
```bash
# 使用默认设置处理文件
python excel_analyzer_final.py

# 创建示例验收数据
python excel_analyzer_final.py --create-sample

# 指定目录和输出文件
python excel_analyzer_final.py -d "my_excel_files" -o "my_report.xlsx"

# 使用验收数据文件
python excel_analyzer_final.py -a "acceptance_data.json"

# 显示详细处理信息
python excel_analyzer_final.py -v

# 仅分析文件结构
python excel_analyzer_final.py --analyze-only
```

### 查看报告
```bash
# 查看详细报告
python view_enhanced_report.py
```

## 📊 输出报告

程序生成的Excel报告包含以下工作表：

### 1. **SKU汇总** 📈
- SKU编码（按指定顺序排序）
- 计划数量
- 验收数量  
- 差异数量
- 差异率(%)

### 2. **处理状态** 📋
- 文件名
- 处理状态
- SKU数量
- 各类数量统计

### 3. **汇总统计** 📊
- 总计划数量
- 总验收数量
- 总差异数量
- 唯一SKU数量
- 处理文件数量

### 4. **SKU排序说明** 📝
- 排序序号
- SKU编码
- 排序说明（指定顺序/字母顺序）

### 5. **错误文件** ⚠️
- 处理失败的文件列表
- 错误原因

## 🔍 处理逻辑

### SKU排序规则
1. **优先级1**: 按指定顺序列表排序
2. **优先级2**: 未在指定列表中的SKU按字母顺序排序

### 数量计算逻辑
1. **有数量列**: 按SKU汇总数量
2. **无数量列**: 统计SKU出现次数
3. **验收数量**: 从验收数据文件获取或使用计划数量
4. **差异计算**: 计划数量 - 验收数量

### 列名识别
- **SKU列**: Баркод, Артикул, SKU, 商品, 产品等
- **数量列**: Количество, 数量, qty, quantity等
- **验收列**: 验收, 实收, received, accepted等
- **差异列**: 差异, difference, variance等

## 📈 示例输出

```
SKU汇总（按指定顺序排序）:
--------------------------------------------------------------------------------
  ✓ MLN101-1    : 计划2530, 验收2500, 差异  30 (  1.2%)
  ✓ MLN101-2    : 计划 180, 验收 180, 差异   0 (  0.0%)
  ✓ MLN101-3    : 计划 899, 验收 890, 差异   9 (  1.0%)
  ...

说明:
  ✓ = 按指定顺序排序的SKU
  ○ = 按字母顺序排序的SKU（未在指定列表中）
```

## ⚙️ 验收数据格式

验收数据文件为JSON格式：
```json
{
  "MLN101-1": 2500,
  "MLN101-2": 180,
  "MLN101-3": 890,
  ...
}
```

## 🛠️ 高级功能

### 命令行参数
```bash
python excel_analyzer_final.py -h  # 查看所有参数
```

### 支持的文件格式
- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)

### 错误处理
- 自动跳过临时文件（~$开头）
- 处理权限错误
- 记录详细错误日志
- 继续处理其他文件

## 📝 日志记录

程序会生成详细的日志文件 `excel_processing.log`，包含：
- 文件处理进度
- 列识别结果
- 错误信息和警告
- 处理统计

## 🔧 故障排除

### 常见问题
1. **找不到Excel文件**: 检查目录路径和文件格式
2. **无法识别SKU列**: 确认列名包含支持的关键词
3. **权限错误**: 关闭Excel文件，删除临时文件
4. **编码问题**: 确保文件使用正确编码

### 调试技巧
- 使用 `--analyze-only` 分析文件结构
- 使用 `-v` 显示详细处理信息
- 查看日志文件了解详细错误

## 🎯 最佳实践

1. **数据准备**: 确保Excel文件格式一致
2. **验收数据**: 提前准备验收数量数据
3. **定期备份**: 保存原始文件和处理结果
4. **验证结果**: 使用查看工具检查报告内容

## 📞 技术支持

如需技术支持或功能改进建议，请查看：
- 日志文件: `excel_processing.log`
- 错误报告: Excel报告中的"错误文件"工作表
- 程序帮助: `python excel_analyzer_final.py -h`

---

**版本**: 最终版本  
**更新时间**: 2025-08-24  
**支持格式**: .xlsx, .xls  
**支持语言**: 中文, 英文, 俄文
