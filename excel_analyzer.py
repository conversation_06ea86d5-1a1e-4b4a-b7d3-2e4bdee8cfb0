#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件SKU分析器
主程序 - 提供命令行界面和批处理功能
"""

import argparse
import os
import sys
from excel_processor import ExcelProcessor

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Excel文件SKU分析器 - 分析和汇总Excel文件中的SKU和数量信息',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python excel_analyzer.py                           # 处理默认目录 'wb 7月送仓'
  python excel_analyzer.py -d "my_excel_files"      # 处理指定目录
  python excel_analyzer.py -o "my_report.xlsx"      # 指定输出文件名
  python excel_analyzer.py -d "data" -o "result.xlsx" # 指定目录和输出文件

支持的文件格式: .xlsx, .xls
        """
    )
    
    parser.add_argument(
        '-d', '--directory',
        default='wb 7月送仓',
        help='包含Excel文件的目录路径 (默认: wb 7月送仓)'
    )
    
    parser.add_argument(
        '-o', '--output',
        default='sku_summary_report.xlsx',
        help='输出报告文件名 (默认: sku_summary_report.xlsx)'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细处理信息'
    )
    
    parser.add_argument(
        '--analyze-only',
        action='store_true',
        help='仅分析文件结构，不进行数据处理'
    )
    
    args = parser.parse_args()
    
    # 检查目录是否存在
    if not os.path.exists(args.directory):
        print(f"错误: 目录 '{args.directory}' 不存在")
        print(f"请确保目录路径正确，或使用 -d 参数指定正确的目录")
        sys.exit(1)
    
    # 检查目录中是否有Excel文件
    import glob
    excel_files = []
    for pattern in ['*.xlsx', '*.xls']:
        files = glob.glob(os.path.join(args.directory, pattern))
        excel_files.extend(files)
    
    if not excel_files:
        print(f"错误: 目录 '{args.directory}' 中没有找到Excel文件")
        print(f"支持的文件格式: .xlsx, .xls")
        sys.exit(1)
    
    print("=" * 80)
    print("Excel文件SKU分析器")
    print("=" * 80)
    print(f"目标目录: {args.directory}")
    print(f"找到 {len(excel_files)} 个Excel文件")
    print(f"输出文件: {args.output}")
    print()
    
    if args.analyze_only:
        print("仅分析模式 - 分析文件结构...")
        from analyze_excel_structure import analyze_excel_file
        
        for file_path in excel_files[:2]:  # 只分析前两个文件作为示例
            result = analyze_excel_file(file_path)
            print(f"\n文件: {result['file_name']}")
            print("-" * 60)
            
            if 'error' in result:
                print(f"错误: {result['error']}")
                continue
            
            print(f"工作表数量: {result['sheet_count']}")
            print(f"工作表名称: {result['sheet_names']}")
            
            for sheet_name, sheet_info in result['sheets_analysis'].items():
                if 'error' in sheet_info:
                    print(f"  工作表 {sheet_name}: 错误 - {sheet_info['error']}")
                    continue
                
                print(f"  工作表 {sheet_name}:")
                print(f"    行数: {sheet_info['row_count']}, 列数: {sheet_info['column_count']}")
                print(f"    可能的SKU列: {sheet_info['potential_sku_columns']}")
                print(f"    可能的数量列: {sheet_info['potential_quantity_columns']}")
        
        return
    
    # 创建处理器并处理文件
    try:
        processor = ExcelProcessor(args.directory)
        
        if args.verbose:
            print("开始处理文件...")
        
        processor.process_all_files()
        
        # 生成并显示报告
        processor.print_summary()
        
        # 保存结果到Excel文件
        processor.save_results_to_excel(args.output)
        
        print(f"\n✅ 处理完成!")
        print(f"📊 汇总结果已保存到: {args.output}")
        print(f"📝 详细日志已保存到: excel_processing.log")
        
        # 显示快速统计
        if processor.sku_summary:
            top_skus = sorted(processor.sku_summary.items(), key=lambda x: x[1], reverse=True)[:5]
            print(f"\n🔝 数量最多的5个SKU:")
            for sku, qty in top_skus:
                print(f"   {sku}: {qty}")
        
        if processor.error_files:
            print(f"\n⚠️  {len(processor.error_files)} 个文件处理失败，请查看日志了解详情")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断处理")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {str(e)}")
        print("请查看日志文件了解详细错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
