# Excel文件SKU分析器

这是一个用于分析和汇总Excel文件中SKU（商品编码）和数量信息的Python程序。

## 功能特点

- 🔍 **自动识别**: 自动识别Excel文件中的SKU列和数量列
- 📊 **数据汇总**: 对相同SKU的数量进行汇总统计
- 🌐 **多语言支持**: 支持中文、英文、俄文等多种语言的列名
- 📁 **批量处理**: 一次性处理目录中的所有Excel文件
- 📈 **详细报告**: 生成包含汇总数据和处理状态的Excel报告
- 🛡️ **错误处理**: 完善的错误处理和日志记录
- 🔧 **灵活配置**: 支持命令行参数自定义处理选项

## 文件说明

- `excel_analyzer.py` - 主程序，提供命令行界面
- `excel_processor.py` - 核心处理逻辑
- `analyze_excel_structure.py` - 文件结构分析工具
- `view_report.py` - 报告查看工具

## 安装依赖

```bash
pip install pandas openpyxl
```

## 使用方法

### 基本用法

```bash
# 处理默认目录 'wb 7月送仓' 中的所有Excel文件
python excel_analyzer.py

# 处理指定目录
python excel_analyzer.py -d "my_excel_files"

# 指定输出文件名
python excel_analyzer.py -o "my_report.xlsx"

# 显示详细处理信息
python excel_analyzer.py -v

# 仅分析文件结构，不进行数据处理
python excel_analyzer.py --analyze-only
```

### 高级用法

```bash
# 处理指定目录并指定输出文件
python excel_analyzer.py -d "data" -o "result.xlsx" -v

# 查看帮助信息
python excel_analyzer.py --help
```

## 支持的文件格式

- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)

## 支持的列名模式

### SKU列识别关键词
- 中文: SKU, 商品, 产品, 货号, 编码, 代码
- 英文: sku, code, item
- 俄文: Баркод, Артикул, Артикул поставщика

### 数量列识别关键词
- 中文: 数量, 件数, 个数, 总数
- 英文: qty, quantity, count, amount
- 俄文: Количество, шт

## 输出文件

程序会生成以下文件：

1. **SKU汇总报告** (`sku_summary_report.xlsx`)
   - `SKU汇总` 工作表: 包含所有唯一SKU和对应的总数量
   - `处理状态` 工作表: 显示每个文件的处理状态和统计信息
   - `错误文件` 工作表: 列出处理失败的文件（如果有）

2. **处理日志** (`excel_processing.log`)
   - 详细的处理过程记录
   - 错误信息和警告

## 处理逻辑

1. **文件发现**: 扫描指定目录中的所有Excel文件
2. **结构分析**: 分析每个文件的工作表结构
3. **列识别**: 自动识别SKU列和数量列
4. **数据提取**: 
   - 如果找到数量列：按SKU汇总数量
   - 如果没有数量列：统计每个SKU的出现次数
5. **数据汇总**: 将所有文件的数据合并汇总
6. **报告生成**: 生成Excel格式的汇总报告

## 示例输出

```
================================================================================
Excel文件处理汇总报告
================================================================================
处理时间: 2025-08-23 22:08:52
成功处理文件数: 6
错误文件数: 0
唯一SKU总数: 22
总数量: 7243

SKU汇总 (前20个):
  MLN101-1: 2530
  MLN101-3: 899
  MLN102-2: 720
  MLN102-4: 588
  MNT101-1: 355
  ...
```

## 查看报告

使用提供的查看工具：

```bash
python view_report.py
```

## 故障排除

1. **找不到Excel文件**: 确保目录路径正确，文件格式为.xlsx或.xls
2. **无法识别SKU列**: 检查列名是否包含支持的关键词
3. **处理失败**: 查看日志文件了解详细错误信息
4. **编码问题**: 确保Excel文件使用UTF-8编码

## 技术特点

- 使用pandas库进行高效的数据处理
- 支持多工作表Excel文件
- 智能列名匹配算法
- 完善的错误处理机制
- 详细的日志记录
- 命令行参数支持

## 注意事项

- 程序会自动跳过空的工作表
- 对于无法识别SKU列的文件会记录警告
- 数量列必须包含数值数据
- 处理大文件时可能需要较长时间

## 许可证

本程序仅供学习和内部使用。
