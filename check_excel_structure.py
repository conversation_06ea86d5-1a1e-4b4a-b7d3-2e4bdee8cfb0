#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel文件结构的脚本
"""

import pandas as pd
import os

def check_excel_file(file_path):
    """检查Excel文件的结构"""
    print(f"\n{'='*60}")
    print(f"检查文件: {os.path.basename(file_path)}")
    print(f"{'='*60}")
    
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表: {excel_file.sheet_names}")
        
        # 检查第一个工作表
        df = pd.read_excel(file_path, sheet_name=0)
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        print("\n前5行数据:")
        print(df.head().to_string())
        
        print("\n数据类型:")
        print(df.dtypes)
        
    except Exception as e:
        print(f"错误: {e}")

# 检查发货文件
check_excel_file("ozon 7月送仓/2000026537595.xlsx")

# 检查验收文件
check_excel_file("ozon 7月送仓/supply-2000024950553-acceptance-report.xlsx")
