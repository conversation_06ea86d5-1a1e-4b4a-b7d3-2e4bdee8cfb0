#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件SKU分析器 - 最终版本
支持验收数量、差异计算和自定义SKU排序
"""

import argparse
import os
import sys
import json
from excel_processor_enhanced import ExcelProcessorEnhanced

def load_acceptance_data(file_path):
    """
    从JSON文件加载验收数据
    
    Args:
        file_path (str): JSON文件路径
        
    Returns:
        dict: 验收数据
    """
    if not os.path.exists(file_path):
        return {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"已加载验收数据: {len(data)} 个SKU")
        return data
    except Exception as e:
        print(f"加载验收数据失败: {str(e)}")
        return {}

def create_sample_acceptance_data():
    """创建示例验收数据"""
    sample_data = {
        'MLN01-1': 220,
        'MLN01-2': 150,
        'MLN101-1': 2500,
        'MLN101-3': 890,
        'MLN102-2': 720,
        'MLN102-4': 588,
        'MNT101-1': 355,
        'MLN101-5': 248,
        'MNT102-1': 210,
        'MLN101-2': 180,
        'MLN101-4': 180,
        'MLN102-1': 172,
        'MLN102-3': 143,
        'MLN01-3': 142,
        'MNT100-1': 112,
        'MLN01-4': 96,
        'MLN101-6': 71,
        'MNT105-1': 68,
        'MNT101-2': 50,
        'MLN103-3': 48,
        'ODD100-2': 36,
        'MLN02-3': 17
    }
    
    with open('sample_acceptance_data.json', 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print("已创建示例验收数据文件: sample_acceptance_data.json")
    return sample_data

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Excel文件SKU分析器 - 最终版本（支持验收数量和自定义排序）',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python excel_analyzer_final.py                                    # 使用默认设置
  python excel_analyzer_final.py -d "my_excel_files"               # 指定目录
  python excel_analyzer_final.py -a "acceptance_data.json"         # 指定验收数据文件
  python excel_analyzer_final.py --create-sample                   # 创建示例验收数据
  python excel_analyzer_final.py -o "final_report.xlsx"            # 指定输出文件

功能特点:
  ✓ 按指定顺序排序SKU（MLN101-1, MLN101-2, ...）
  ✓ 支持验收数量和差异计算
  ✓ 多语言列名识别（中文、英文、俄文）
  ✓ 详细的Excel报告和统计分析
  ✓ 完善的错误处理和日志记录
        """
    )
    
    parser.add_argument(
        '-d', '--directory',
        default='wb 7月送仓',
        help='包含Excel文件的目录路径 (默认: wb 7月送仓)'
    )
    
    parser.add_argument(
        '-a', '--acceptance-data',
        help='验收数据JSON文件路径'
    )
    
    parser.add_argument(
        '-o', '--output',
        default='sku_summary_report_final.xlsx',
        help='输出报告文件名 (默认: sku_summary_report_final.xlsx)'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细处理信息'
    )
    
    parser.add_argument(
        '--create-sample',
        action='store_true',
        help='创建示例验收数据文件'
    )
    
    parser.add_argument(
        '--analyze-only',
        action='store_true',
        help='仅分析文件结构，不进行数据处理'
    )
    
    args = parser.parse_args()
    
    # 创建示例验收数据
    if args.create_sample:
        create_sample_acceptance_data()
        return
    
    # 检查目录是否存在
    if not os.path.exists(args.directory):
        print(f"错误: 目录 '{args.directory}' 不存在")
        print(f"请确保目录路径正确，或使用 -d 参数指定正确的目录")
        sys.exit(1)
    
    # 检查目录中是否有Excel文件
    import glob
    excel_files = []
    for pattern in ['*.xlsx', '*.xls']:
        files = glob.glob(os.path.join(args.directory, pattern))
        # 过滤掉临时文件
        files = [f for f in files if not os.path.basename(f).startswith('~$')]
        excel_files.extend(files)
    
    if not excel_files:
        print(f"错误: 目录 '{args.directory}' 中没有找到Excel文件")
        print(f"支持的文件格式: .xlsx, .xls")
        sys.exit(1)
    
    print("=" * 80)
    print("Excel文件SKU分析器 - 最终版本")
    print("=" * 80)
    print(f"目标目录: {args.directory}")
    print(f"找到 {len(excel_files)} 个Excel文件")
    print(f"输出文件: {args.output}")
    
    # 加载验收数据
    acceptance_data = {}
    if args.acceptance_data:
        acceptance_data = load_acceptance_data(args.acceptance_data)
    else:
        # 尝试加载默认的示例数据
        if os.path.exists('sample_acceptance_data.json'):
            acceptance_data = load_acceptance_data('sample_acceptance_data.json')
        else:
            print("未指定验收数据文件，将使用计划数量作为验收数量")
    
    print()
    
    if args.analyze_only:
        print("仅分析模式 - 分析文件结构...")
        from analyze_excel_structure import analyze_excel_file
        
        for file_path in excel_files[:2]:  # 只分析前两个文件作为示例
            result = analyze_excel_file(file_path)
            print(f"\n文件: {result['file_name']}")
            print("-" * 60)
            
            if 'error' in result:
                print(f"错误: {result['error']}")
                continue
            
            print(f"工作表数量: {result['sheet_count']}")
            print(f"工作表名称: {result['sheet_names']}")
            
            for sheet_name, sheet_info in result['sheets_analysis'].items():
                if 'error' in sheet_info:
                    print(f"  工作表 {sheet_name}: 错误 - {sheet_info['error']}")
                    continue
                
                print(f"  工作表 {sheet_name}:")
                print(f"    行数: {sheet_info['row_count']}, 列数: {sheet_info['column_count']}")
                print(f"    可能的SKU列: {sheet_info['potential_sku_columns']}")
                print(f"    可能的数量列: {sheet_info['potential_quantity_columns']}")
                if 'potential_acceptance_columns' in sheet_info:
                    print(f"    可能的验收列: {sheet_info['potential_acceptance_columns']}")
                if 'potential_difference_columns' in sheet_info:
                    print(f"    可能的差异列: {sheet_info['potential_difference_columns']}")
        
        return
    
    # 创建处理器并处理文件
    try:
        processor = ExcelProcessorEnhanced(args.directory, acceptance_data)
        
        if args.verbose:
            print("开始处理文件...")
        
        processor.process_all_files()
        
        # 生成并显示报告
        processor.print_summary()
        
        # 保存结果到Excel文件
        processor.save_results_to_excel(args.output)
        
        print(f"\n✅ 处理完成!")
        print(f"📊 汇总结果已保存到: {args.output}")
        print(f"📝 详细日志已保存到: excel_processing.log")
        
        # 显示排序说明
        print(f"\n📋 SKU排序说明:")
        print(f"   ✓ 按指定顺序排序: MLN101-1, MLN101-2, MLN101-3, ...")
        print(f"   ○ 未在指定列表中的SKU按字母顺序排序")
        
        # 显示快速统计
        if processor.sku_summary:
            sorted_skus = processor.sort_skus(processor.sku_summary)
            specified_count = sum(1 for sku, _ in sorted_skus if sku in processor.sku_order)
            unspecified_count = len(sorted_skus) - specified_count
            
            print(f"\n📈 快速统计:")
            print(f"   指定顺序的SKU: {specified_count}")
            print(f"   其他SKU: {unspecified_count}")
            print(f"   总SKU数量: {len(sorted_skus)}")
        
        if processor.error_files:
            print(f"\n⚠️  {len(processor.error_files)} 个文件处理失败，请查看日志了解详情")
        
        print(f"\n💡 提示: 使用 'python view_enhanced_report.py' 查看详细报告")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断处理")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {str(e)}")
        print("请查看日志文件了解详细错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
