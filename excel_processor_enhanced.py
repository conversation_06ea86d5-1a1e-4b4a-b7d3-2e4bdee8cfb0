#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版Excel文件处理程序
支持验收数量、差异计算和自定义SKU排序
"""

import pandas as pd
import os
import logging
from pathlib import Path
from collections import defaultdict
import glob
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ExcelProcessorEnhanced:
    """增强版Excel文件处理器"""
    
    def __init__(self, directory_path, acceptance_data=None):
        """
        初始化处理器
        
        Args:
            directory_path (str): 包含Excel文件的目录路径
            acceptance_data (dict): 验收数据，格式为 {sku: received_quantity}
        """
        self.directory_path = directory_path
        self.sku_summary = defaultdict(lambda: {'planned': 0, 'received': 0, 'difference': 0})
        self.processed_files = []
        self.error_files = []
        self.acceptance_data = acceptance_data or {}
        
        # 指定的SKU排序顺序
        self.sku_order = [
            'MLN101-1', 'MLN101-2', 'MLN101-3', 'MLN101-4', 'MLN101-5', 'MLN101-6',
            'MLN102-1', 'MLN102-2', 'MLN102-3', 'MLN102-4', 'MLN108-1',
            'MNT100-1', 'MNT101-1', 'MNT101-2', 'MNT102-1', 'ODD100-1',
            'MLN01-1', 'MLN01-2', 'MLN01-3', 'MLN01-4', 'MLN02-3',
            'MLN103-3', 'ODD100-2', 'MNT105-1'
        ]
        
        # 定义可能的SKU和数量列名模式
        self.sku_patterns = [
            'sku', 'SKU', '商品', '产品', '货号', '编码', '代码', 'code', 'item',
            'Баркод', 'Артикул', 'Артикул поставщика', 'баркод', 'артикул'
        ]
        
        self.quantity_patterns = [
            '数量', 'qty', 'quantity', '件数', '个数', 'count', 'amount', '总数',
            'Количество', 'количество', 'шт'
        ]
        
        # 验收相关列名模式
        self.acceptance_patterns = [
            '验收', '实收', '到货', '收货', '入库', 'received', 'accepted', 'arrival',
            'Получено', 'получено', 'принято'
        ]
        
        # 差异相关列名模式
        self.difference_patterns = [
            '差异', '差额', '缺失', '多余', 'difference', 'variance', 'shortage', 'excess',
            'Разница', 'разница', 'отклонение'
        ]
    
    def get_sku_sort_key(self, sku):
        """
        获取SKU的排序键值
        
        Args:
            sku (str): SKU编码
            
        Returns:
            tuple: 排序键值 (优先级, SKU名称)
        """
        try:
            # 如果SKU在指定顺序列表中，返回其索引作为优先级
            index = self.sku_order.index(sku)
            return (0, index, sku)  # 0表示最高优先级
        except ValueError:
            # 如果SKU不在列表中，放在末尾按字母顺序排序
            return (1, 0, sku)  # 1表示较低优先级
    
    def sort_skus(self, sku_dict):
        """
        按照指定顺序对SKU字典进行排序
        
        Args:
            sku_dict (dict): SKU字典
            
        Returns:
            list: 排序后的(sku, data)元组列表
        """
        return sorted(sku_dict.items(), key=lambda x: self.get_sku_sort_key(x[0]))
    
    def find_excel_files(self):
        """
        查找目录中的所有Excel文件
        
        Returns:
            list: Excel文件路径列表
        """
        excel_files = []
        patterns = ['*.xlsx', '*.xls']
        
        for pattern in patterns:
            files = glob.glob(os.path.join(self.directory_path, pattern))
            excel_files.extend(files)
        
        logger.info(f"找到 {len(excel_files)} 个Excel文件")
        return excel_files
    
    def identify_columns(self, df):
        """
        识别DataFrame中的SKU、数量、验收和差异列
        
        Args:
            df (pandas.DataFrame): 数据框
            
        Returns:
            tuple: (sku_column, quantity_column, acceptance_column, difference_column)
        """
        sku_column = None
        quantity_column = None
        acceptance_column = None
        difference_column = None
        
        # 查找SKU列
        for col in df.columns:
            col_str = str(col).lower()
            if any(pattern.lower() in col_str or col_str in pattern.lower() 
                   for pattern in self.sku_patterns):
                sku_column = col
                break
        
        # 查找数量列（计划数量）
        for col in df.columns:
            col_str = str(col).lower()
            if any(pattern.lower() in col_str or col_str in pattern.lower() 
                   for pattern in self.quantity_patterns):
                # 检查列是否包含数值数据
                if pd.api.types.is_numeric_dtype(df[col]):
                    quantity_column = col
                    break
        
        # 查找验收数量列
        for col in df.columns:
            col_str = str(col).lower()
            if any(pattern.lower() in col_str or col_str in pattern.lower() 
                   for pattern in self.acceptance_patterns):
                if pd.api.types.is_numeric_dtype(df[col]):
                    acceptance_column = col
                    break
        
        # 查找差异数量列
        for col in df.columns:
            col_str = str(col).lower()
            if any(pattern.lower() in col_str or col_str in pattern.lower() 
                   for pattern in self.difference_patterns):
                if pd.api.types.is_numeric_dtype(df[col]):
                    difference_column = col
                    break
        
        return sku_column, quantity_column, acceptance_column, difference_column
    
    def process_file(self, file_path):
        """
        处理单个Excel文件
        
        Args:
            file_path (str): Excel文件路径
            
        Returns:
            dict: 处理结果
        """
        try:
            logger.info(f"正在处理文件: {os.path.basename(file_path)}")
            
            # 读取Excel文件的所有工作表
            excel_file = pd.ExcelFile(file_path)
            file_sku_count = defaultdict(lambda: {'planned': 0, 'received': 0, 'difference': 0})
            
            for sheet_name in excel_file.sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    if df.empty:
                        logger.warning(f"工作表 {sheet_name} 为空，跳过")
                        continue
                    
                    sku_column, quantity_column, acceptance_column, difference_column = self.identify_columns(df)
                    
                    if sku_column is None:
                        logger.warning(f"在 {sheet_name} 中未找到SKU列，跳过")
                        continue
                    
                    logger.info(f"在工作表 {sheet_name} 中找到SKU列: {sku_column}")
                    
                    if acceptance_column:
                        logger.info(f"找到验收数量列: {acceptance_column}")
                    if difference_column:
                        logger.info(f"找到差异数量列: {difference_column}")
                    
                    if quantity_column:
                        logger.info(f"找到计划数量列: {quantity_column}")
                        # 按SKU汇总各种数量
                        agg_dict = {quantity_column: 'sum'}
                        if acceptance_column:
                            agg_dict[acceptance_column] = 'sum'
                        if difference_column:
                            agg_dict[difference_column] = 'sum'

                        grouped = df.groupby(sku_column).agg(agg_dict).fillna(0)
                        
                        for sku, row in grouped.iterrows():
                            if pd.notna(sku):
                                sku_str = str(sku)
                                planned_qty = int(row[quantity_column]) if pd.notna(row[quantity_column]) else 0
                                received_qty = int(row[acceptance_column]) if acceptance_column and pd.notna(row[acceptance_column]) else 0
                                diff_qty = int(row[difference_column]) if difference_column and pd.notna(row[difference_column]) else 0
                                
                                # 如果没有验收数量列，从外部数据获取
                                if not acceptance_column:
                                    received_qty = self.acceptance_data.get(sku_str, planned_qty)
                                
                                # 如果没有差异列，计算差异
                                if not difference_column:
                                    diff_qty = planned_qty - received_qty
                                
                                file_sku_count[sku_str]['planned'] += planned_qty
                                file_sku_count[sku_str]['received'] += received_qty
                                file_sku_count[sku_str]['difference'] += diff_qty
                    else:
                        logger.info("未找到计划数量列，按SKU出现次数计算")
                        # 计算每个SKU的出现次数作为计划数量
                        sku_counts = df[sku_column].value_counts()
                        for sku, count in sku_counts.items():
                            if pd.notna(sku):
                                sku_str = str(sku)
                                planned_qty = count
                                # 从验收数据中获取验收数量
                                received_qty = self.acceptance_data.get(sku_str, planned_qty)
                                diff_qty = planned_qty - received_qty
                                
                                file_sku_count[sku_str]['planned'] += planned_qty
                                file_sku_count[sku_str]['received'] += received_qty
                                file_sku_count[sku_str]['difference'] += diff_qty
                
                except Exception as e:
                    logger.error(f"处理工作表 {sheet_name} 时出错: {str(e)}")
            
            # 更新总汇总
            for sku, data in file_sku_count.items():
                self.sku_summary[sku]['planned'] += data['planned']
                self.sku_summary[sku]['received'] += data['received']
                self.sku_summary[sku]['difference'] += data['difference']
            
            total_planned = sum(data['planned'] for data in file_sku_count.values())
            total_received = sum(data['received'] for data in file_sku_count.values())
            total_difference = sum(data['difference'] for data in file_sku_count.values())
            
            result = {
                'file_name': os.path.basename(file_path),
                'status': 'success',
                'sku_count': len(file_sku_count),
                'total_planned': total_planned,
                'total_received': total_received,
                'total_difference': total_difference,
                'skus': dict(file_sku_count)
            }
            
            self.processed_files.append(result)
            logger.info(f"成功处理文件 {os.path.basename(file_path)}: {len(file_sku_count)} 个SKU")
            
            return result
            
        except Exception as e:
            error_msg = f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}"
            logger.error(error_msg)
            
            error_result = {
                'file_name': os.path.basename(file_path),
                'status': 'error',
                'error': str(e)
            }
            
            self.error_files.append(error_result)
            return error_result
    
    def process_all_files(self):
        """处理所有Excel文件"""
        excel_files = self.find_excel_files()
        
        if not excel_files:
            logger.warning("未找到Excel文件")
            return
        
        logger.info(f"开始处理 {len(excel_files)} 个文件")
        
        for file_path in excel_files:
            self.process_file(file_path)
        
        logger.info("所有文件处理完成")

    def save_results_to_excel(self, output_file='sku_summary_report_enhanced.xlsx'):
        """
        将结果保存到Excel文件（按指定顺序排序）

        Args:
            output_file (str): 输出文件名
        """
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # SKU汇总表（按指定顺序排序）
                sku_data = []
                sorted_skus = self.sort_skus(self.sku_summary)

                for sku, data in sorted_skus:
                    sku_data.append({
                        'SKU': sku,
                        '计划数量': data['planned'],
                        '验收数量': data['received'],
                        '差异数量': data['difference'],
                        '差异率(%)': round((data['difference'] / data['planned'] * 100) if data['planned'] > 0 else 0, 2)
                    })

                sku_df = pd.DataFrame(sku_data)
                sku_df.to_excel(writer, sheet_name='SKU汇总', index=False)

                # 文件处理状态表
                if self.processed_files:
                    files_df = pd.DataFrame(self.processed_files)
                    files_df.to_excel(writer, sheet_name='处理状态', index=False)

                # 错误文件表
                if self.error_files:
                    error_df = pd.DataFrame(self.error_files)
                    error_df.to_excel(writer, sheet_name='错误文件', index=False)

                # 汇总统计表
                summary_data = [{
                    '项目': '总计划数量',
                    '数值': sum(data['planned'] for data in self.sku_summary.values())
                }, {
                    '项目': '总验收数量',
                    '数值': sum(data['received'] for data in self.sku_summary.values())
                }, {
                    '项目': '总差异数量',
                    '数值': sum(data['difference'] for data in self.sku_summary.values())
                }, {
                    '项目': '唯一SKU数量',
                    '数值': len(self.sku_summary)
                }, {
                    '项目': '处理文件数量',
                    '数值': len(self.processed_files)
                }]

                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

                # SKU排序说明表
                order_data = []
                for i, sku in enumerate(self.sku_order, 1):
                    order_data.append({
                        '排序': i,
                        'SKU': sku,
                        '说明': '指定顺序'
                    })

                # 添加未在指定列表中的SKU
                all_skus = set(self.sku_summary.keys())
                specified_skus = set(self.sku_order)
                unspecified_skus = sorted(all_skus - specified_skus)

                for i, sku in enumerate(unspecified_skus, len(self.sku_order) + 1):
                    order_data.append({
                        '排序': i,
                        'SKU': sku,
                        '说明': '字母顺序（未在指定列表中）'
                    })

                order_df = pd.DataFrame(order_data)
                order_df.to_excel(writer, sheet_name='SKU排序说明', index=False)

            logger.info(f"结果已保存到: {output_file}")

        except Exception as e:
            logger.error(f"保存Excel文件时出错: {str(e)}")

    def print_summary(self):
        """打印汇总信息（按指定顺序排序）"""
        total_planned = sum(data['planned'] for data in self.sku_summary.values())
        total_received = sum(data['received'] for data in self.sku_summary.values())
        total_difference = sum(data['difference'] for data in self.sku_summary.values())

        print("\n" + "="*80)
        print("Excel文件处理汇总报告（增强版）")
        print("="*80)
        print(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"成功处理文件数: {len(self.processed_files)}")
        print(f"错误文件数: {len(self.error_files)}")
        print(f"唯一SKU总数: {len(self.sku_summary)}")
        print(f"总计划数量: {total_planned}")
        print(f"总验收数量: {total_received}")
        print(f"总差异数量: {total_difference}")
        if total_planned > 0:
            print(f"总差异率: {round(total_difference / total_planned * 100, 2)}%")

        if self.error_files:
            print(f"\n错误文件:")
            for error_file in self.error_files:
                print(f"  - {error_file['file_name']}: {error_file['error']}")

        print(f"\nSKU汇总（按指定顺序排序）:")
        print("-" * 80)
        sorted_skus = self.sort_skus(self.sku_summary)

        for sku, data in sorted_skus:
            diff_rate = round((data['difference'] / data['planned'] * 100) if data['planned'] > 0 else 0, 1)
            status = "✓" if sku in self.sku_order else "○"
            print(f"  {status} {sku:<12}: 计划{data['planned']:>4}, 验收{data['received']:>4}, 差异{data['difference']:>4} ({diff_rate:>5.1f}%)")

        print(f"\n说明:")
        print(f"  ✓ = 按指定顺序排序的SKU")
        print(f"  ○ = 按字母顺序排序的SKU（未在指定列表中）")


def main():
    """主函数"""
    directory_path = "wb 7月送仓"

    if not os.path.exists(directory_path):
        print(f"错误: 目录 {directory_path} 不存在")
        return

    # 示例验收数据（可以从文件或用户输入获取）
    acceptance_data = {
        'MLN01-1': 220,
        'MLN01-2': 150,
        'MLN101-1': 2500,
        'MLN101-3': 890,
        # 可以添加更多验收数据
    }

    # 创建处理器并处理文件
    processor = ExcelProcessorEnhanced(directory_path, acceptance_data)
    processor.process_all_files()

    # 生成并显示报告
    processor.print_summary()

    # 保存结果到Excel文件
    processor.save_results_to_excel()

    print(f"\n详细日志已保存到: excel_processing.log")
    print(f"汇总结果已保存到: sku_summary_report_enhanced.xlsx")


if __name__ == "__main__":
    main()
